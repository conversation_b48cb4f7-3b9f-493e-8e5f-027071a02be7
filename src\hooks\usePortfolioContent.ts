import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface PortfolioContent {
  id: string;
  section: string;
  content: any;
  created_at: string;
  updated_at: string;
}

export const usePortfolioContent = () => {
  const queryClient = useQueryClient();

  // Fetch all content
  const { data: content, isLoading, error } = useQuery({
    queryKey: ['portfolio-content'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('portfolio_content')
        .select('*')
        .order('section');

      if (error) throw error;
      return data as PortfolioContent[];
    },
  });

  // Get content by section
  const getContentBySection = (section: string) => {
    return content?.find(item => item.section === section)?.content;
  };

  // Update content mutation
  const updateContentMutation = useMutation({
    mutationFn: async ({ section, content }: { section: string; content: any }) => {
      const { data, error } = await supabase
        .from('portfolio_content')
        .upsert({
          section,
          content,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolio-content'] });
      toast.success('Content updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating content:', error);
      toast.error('Failed to update content');
    },
  });

  return {
    content,
    isLoading,
    error,
    getContentBySection,
    updateContent: updateContentMutation.mutate,
    isUpdating: updateContentMutation.isPending,
  };
};

export const usePortfolioItems = () => {
  const queryClient = useQueryClient();

  // Fetch portfolio items
  const { data: portfolioItems, isLoading, error } = useQuery({
    queryKey: ['portfolio-items'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('portfolio_items')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  // Create portfolio item
  const createItemMutation = useMutation({
    mutationFn: async (item: any) => {
      const { data, error } = await supabase
        .from('portfolio_items')
        .insert(item)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolio-items'] });
      toast.success('Portfolio item created successfully!');
    },
    onError: (error) => {
      console.error('Error creating portfolio item:', error);
      toast.error('Failed to create portfolio item');
    },
  });

  // Update portfolio item
  const updateItemMutation = useMutation({
    mutationFn: async ({ id, ...updates }: any) => {
      const { data, error } = await supabase
        .from('portfolio_items')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolio-items'] });
      toast.success('Portfolio item updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating portfolio item:', error);
      toast.error('Failed to update portfolio item');
    },
  });

  // Delete portfolio item
  const deleteItemMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('portfolio_items')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolio-items'] });
      toast.success('Portfolio item deleted successfully!');
    },
    onError: (error) => {
      console.error('Error deleting portfolio item:', error);
      toast.error('Failed to delete portfolio item');
    },
  });

  return {
    portfolioItems,
    isLoading,
    error,
    createItem: createItemMutation.mutate,
    updateItem: updateItemMutation.mutate,
    deleteItem: deleteItemMutation.mutate,
    isCreating: createItemMutation.isPending,
    isUpdating: updateItemMutation.isPending,
    isDeleting: deleteItemMutation.isPending,
  };
};
