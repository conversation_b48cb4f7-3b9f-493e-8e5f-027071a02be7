import React, { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  Settings,
  Save,
  Palette,
  Bell,
  Shield,
  Database,
  Download,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import {
  useGeneralSettings,
  useAppearanceSettings,
  useSocialSettings,
  useSEOSettings,
} from "@/hooks/useSiteSettings";

const AdminSettings = () => {
  const [activeTab, setActiveTab] = useState("general");

  // Database hooks
  const {
    generalSettings,
    updateGeneralSettings,
    isUpdating: isUpdatingGeneral,
  } = useGeneralSettings();

  const {
    appearanceSettings,
    updateAppearanceSettings,
    isUpdating: isUpdatingAppearance,
  } = useAppearanceSettings();

  const {
    socialSettings,
    updateSocialSettings,
    isUpdating: isUpdatingSocial,
  } = useSocialSettings();

  const {
    seoSettings,
    updateSEOSettings,
    isUpdating: isUpdatingSEO,
  } = useSEOSettings();

  const settingsTabs = [
    { id: "general", label: "General", icon: Settings },
    { id: "appearance", label: "Appearance", icon: Palette },
    { id: "social", label: "Social Media", icon: Bell },
    { id: "seo", label: "SEO", icon: Shield },
    { id: "backup", label: "Backup", icon: Download },
  ];

  // Local state for editing with safe defaults
  const [localGeneralSettings, setLocalGeneralSettings] = useState({
    siteName: "InBio Portfolio",
    siteDescription: "Professional Portfolio & Resume",
    contactEmail: "<EMAIL>",
    timezone: "UTC",
    language: "en",
    maintenanceMode: false,
    ...generalSettings,
  });

  const [localAppearanceSettings, setLocalAppearanceSettings] = useState({
    primaryColor: "#ef4444",
    darkMode: true,
    showAnimations: true,
    compactMode: false,
    customCSS: "",
    ...appearanceSettings,
  });

  const [localSocialSettings, setLocalSocialSettings] = useState({
    linkedin: "",
    github: "",
    twitter: "",
    instagram: "",
    ...socialSettings,
  });

  const [localSEOSettings, setLocalSEOSettings] = useState({
    metaTitle: "Professional Portfolio",
    metaDescription: "Professional portfolio showcasing my work and skills",
    ...seoSettings,
  });

  // Local notification settings (not connected to database yet)
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    newMessages: true,
    systemAlerts: true,
    weeklyReports: false,
    instantAlerts: true,
  });

  // Update local state when database data changes
  useEffect(() => {
    if (generalSettings) {
      setLocalGeneralSettings((prev) => ({ ...prev, ...generalSettings }));
    }
  }, [generalSettings]);

  useEffect(() => {
    if (appearanceSettings) {
      setLocalAppearanceSettings((prev) => ({
        ...prev,
        ...appearanceSettings,
      }));
    }
  }, [appearanceSettings]);

  useEffect(() => {
    if (socialSettings) {
      setLocalSocialSettings((prev) => ({ ...prev, ...socialSettings }));
    }
  }, [socialSettings]);

  useEffect(() => {
    if (seoSettings) {
      setLocalSEOSettings((prev) => ({ ...prev, ...seoSettings }));
    }
  }, [seoSettings]);

  const handleSave = async () => {
    try {
      // Save settings based on active tab
      switch (activeTab) {
        case "general":
          updateGeneralSettings(localGeneralSettings);
          break;
        case "appearance":
          updateAppearanceSettings(localAppearanceSettings);
          break;
        case "social":
          updateSocialSettings(localSocialSettings);
          break;
        case "seo":
          updateSEOSettings(localSEOSettings);
          break;
        default:
          toast.info(`${activeTab} settings will be implemented soon`);
          return;
      }
    } catch (error) {
      console.error("Error saving settings:", error);
      toast.error("Failed to save settings");
    }
  };

  const isLoading =
    isUpdatingGeneral ||
    isUpdatingAppearance ||
    isUpdatingSocial ||
    isUpdatingSEO;

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="siteName" className="text-white">
            Site Name
          </Label>
          <Input
            id="siteName"
            value={localGeneralSettings.siteName}
            onChange={(e) =>
              setLocalGeneralSettings((prev) => ({
                ...prev,
                siteName: e.target.value,
              }))
            }
            className="bg-dark-bg border-red/30 text-white"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="contactEmail" className="text-white">
            Contact Email
          </Label>
          <Input
            id="contactEmail"
            type="email"
            value={localGeneralSettings.contactEmail}
            onChange={(e) =>
              setLocalGeneralSettings((prev) => ({
                ...prev,
                contactEmail: e.target.value,
              }))
            }
            className="bg-dark-bg border-red/30 text-white"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="siteDescription" className="text-white">
          Site Description
        </Label>
        <Textarea
          id="siteDescription"
          value={localGeneralSettings.siteDescription}
          onChange={(e) =>
            setLocalGeneralSettings((prev) => ({
              ...prev,
              siteDescription: e.target.value,
            }))
          }
          className="bg-dark-bg border-red/30 text-white"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="timezone" className="text-white">
            Timezone
          </Label>
          <select
            id="timezone"
            value={localGeneralSettings.timezone}
            onChange={(e) =>
              setLocalGeneralSettings((prev) => ({
                ...prev,
                timezone: e.target.value,
              }))
            }
            className="w-full px-3 py-2 bg-dark-bg border border-red/30 rounded-lg text-white"
          >
            <option value="UTC">UTC</option>
            <option value="EST">Eastern Time</option>
            <option value="PST">Pacific Time</option>
            <option value="GMT">Greenwich Mean Time</option>
          </select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="language" className="text-white">
            Language
          </Label>
          <select
            id="language"
            value={localGeneralSettings.language}
            onChange={(e) =>
              setLocalGeneralSettings((prev) => ({
                ...prev,
                language: e.target.value,
              }))
            }
            className="w-full px-3 py-2 bg-dark-bg border border-red/30 rounded-lg text-white"
          >
            <option value="en">English</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
          </select>
        </div>
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div className="flex items-center gap-3">
          <div>
            <p className="text-white font-medium">Maintenance Mode</p>
            <p className="text-gray-400 text-sm">
              Temporarily disable public access
            </p>
          </div>
        </div>
        <Switch
          checked={localGeneralSettings.maintenanceMode}
          onCheckedChange={(checked) =>
            setLocalGeneralSettings((prev) => ({
              ...prev,
              maintenanceMode: checked,
            }))
          }
        />
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="primaryColor" className="text-white">
            Primary Color
          </Label>
          <div className="flex gap-2">
            <Input
              id="primaryColor"
              type="color"
              value={localAppearanceSettings.primaryColor}
              onChange={(e) =>
                setLocalAppearanceSettings((prev) => ({
                  ...prev,
                  primaryColor: e.target.value,
                }))
              }
              className="w-16 h-10 bg-dark-bg border-red/30"
            />
            <Input
              value={localAppearanceSettings.primaryColor}
              onChange={(e) =>
                setLocalAppearanceSettings((prev) => ({
                  ...prev,
                  primaryColor: e.target.value,
                }))
              }
              className="flex-1 bg-dark-bg border-red/30 text-white"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
          <div>
            <p className="text-white font-medium">Dark Mode</p>
            <p className="text-gray-400 text-sm">
              Use dark theme for admin panel
            </p>
          </div>
          <Switch
            checked={localAppearanceSettings.darkMode}
            onCheckedChange={(checked) =>
              setLocalAppearanceSettings((prev) => ({
                ...prev,
                darkMode: checked,
              }))
            }
          />
        </div>

        <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
          <div>
            <p className="text-white font-medium">Show Animations</p>
            <p className="text-gray-400 text-sm">
              Enable UI animations and transitions
            </p>
          </div>
          <Switch
            checked={localAppearanceSettings.showAnimations}
            onCheckedChange={(checked) =>
              setLocalAppearanceSettings((prev) => ({
                ...prev,
                showAnimations: checked,
              }))
            }
          />
        </div>

        <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
          <div>
            <p className="text-white font-medium">Compact Mode</p>
            <p className="text-gray-400 text-sm">
              Reduce spacing for more content
            </p>
          </div>
          <Switch
            checked={localAppearanceSettings.compactMode}
            onCheckedChange={(checked) =>
              setLocalAppearanceSettings((prev) => ({
                ...prev,
                compactMode: checked,
              }))
            }
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="customCSS" className="text-white">
          Custom CSS
        </Label>
        <Textarea
          id="customCSS"
          value={localAppearanceSettings.customCSS}
          onChange={(e) =>
            setLocalAppearanceSettings((prev) => ({
              ...prev,
              customCSS: e.target.value,
            }))
          }
          placeholder="/* Add your custom CSS here */"
          className="bg-dark-bg border-red/30 text-white font-mono"
          rows={6}
        />
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">Email Notifications</p>
          <p className="text-gray-400 text-sm">
            Receive notifications via email
          </p>
        </div>
        <Switch
          checked={notificationSettings.emailNotifications}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              emailNotifications: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">New Messages</p>
          <p className="text-gray-400 text-sm">
            Get notified of new contact messages
          </p>
        </div>
        <Switch
          checked={notificationSettings.newMessages}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              newMessages: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">System Alerts</p>
          <p className="text-gray-400 text-sm">
            Important system notifications
          </p>
        </div>
        <Switch
          checked={notificationSettings.systemAlerts}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              systemAlerts: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">Weekly Reports</p>
          <p className="text-gray-400 text-sm">
            Receive weekly analytics reports
          </p>
        </div>
        <Switch
          checked={notificationSettings.weeklyReports}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              weeklyReports: checked,
            }))
          }
        />
      </div>

      <div className="flex items-center justify-between p-4 bg-dark-bg/50 rounded-lg border border-red/20">
        <div>
          <p className="text-white font-medium">Instant Alerts</p>
          <p className="text-gray-400 text-sm">
            Real-time browser notifications
          </p>
        </div>
        <Switch
          checked={notificationSettings.instantAlerts}
          onCheckedChange={(checked) =>
            setNotificationSettings((prev) => ({
              ...prev,
              instantAlerts: checked,
            }))
          }
        />
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case "general":
        return renderGeneralSettings();
      case "appearance":
        return renderAppearanceSettings();
      case "social":
        return renderNotificationSettings(); // Temporary - will be replaced with social settings
      default:
        return (
          <div className="text-center py-12">
            <Settings className="w-12 h-12 text-red/50 mx-auto mb-4" />
            <p className="text-gray-400">{activeTab} settings coming soon...</p>
          </div>
        );
    }
  };

  return (
    <AdminLayout title="Settings">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-white">Settings</h2>
            <p className="text-gray-400">
              Configure your admin panel and site settings
            </p>
          </div>
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="bg-red hover:bg-red/80"
          >
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Saving...
              </div>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>

        {/* Settings Tabs */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl overflow-hidden">
          <div className="flex overflow-x-auto border-b border-red/20">
            {settingsTabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center gap-2 px-6 py-4 whitespace-nowrap transition-all ${
                    activeTab === tab.id
                      ? "bg-red/20 text-red border-b-2 border-red"
                      : "text-gray-400 hover:text-white hover:bg-red/10"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          <div className="p-6">{renderContent()}</div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
