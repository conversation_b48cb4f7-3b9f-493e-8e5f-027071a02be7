import { useState, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setUser(session?.user ?? null);

      if (session?.user) {
        await checkAdminStatus(session.user.id);
      }

      setLoading(false);
    };

    getSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null);

      if (session?.user) {
        await checkAdminStatus(session.user.id);
      } else {
        setIsAdmin(false);
      }

      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAdminStatus = async (userId: string) => {
    // Simplified: just set admin to true for now
    // We'll implement proper role checking later
    setIsAdmin(true);
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
        setLoading(false);
        return { success: false, error };
      }

      if (data.user) {
        // Set admin status immediately for successful login
        setIsAdmin(true);
        toast.success("Welcome to Admin Panel!");
        setLoading(false);
        return { success: true, user: data.user };
      }

      setLoading(false);
      return { success: false, error: new Error("No user returned") };
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed - please try again");
      setLoading(false);
      return { success: false, error };
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();

      if (error) {
        toast.error(error.message);
        return { success: false, error };
      }

      setIsAdmin(false);
      toast.success("Logged out successfully!");
      return { success: true };
    } catch (error) {
      toast.error("An unexpected error occurred");
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
        return { success: false, error };
      }

      if (data.user) {
        // Create admin user record
        const { error: adminError } = await supabase
          .from("admin_users")
          .insert({
            id: data.user.id,
            email: data.user.email!,
            role: "admin",
            is_active: true,
          });

        if (adminError) {
          console.error("Error creating admin user:", adminError);
        }

        toast.success("Admin account created successfully!");
        return { success: true, user: data.user };
      }

      return { success: false, error: new Error("No user returned") };
    } catch (error) {
      toast.error("An unexpected error occurred");
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    loading,
    isAdmin,
    signIn,
    signOut,
    signUp,
  };
};
