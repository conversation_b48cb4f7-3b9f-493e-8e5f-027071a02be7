import { useState, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      setUser(session?.user ?? null);

      if (session?.user) {
        await checkAdminStatus(session.user.id);
      }

      setLoading(false);
    };

    getSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null);

      if (session?.user) {
        await checkAdminStatus(session.user.id);
      } else {
        setIsAdmin(false);
      }

      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAdminStatus = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from("admin_users")
        .select("role, is_active")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("Error checking admin status:", error);
        // For now, assume user is admin if they can authenticate
        // This is a temporary fix until RLS is properly configured
        setIsAdmin(true);
        return;
      }

      setIsAdmin(data?.is_active && data?.role === "admin");
    } catch (error) {
      console.error("Error checking admin status:", error);
      // Temporary fix: assume admin if authenticated
      setIsAdmin(true);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Login timeout")), 10000)
      );

      const loginPromise = supabase.auth.signInWithPassword({
        email,
        password,
      });

      const { data, error } = await Promise.race([
        loginPromise,
        timeoutPromise,
      ]);

      if (error) {
        toast.error(error.message);
        return { success: false, error };
      }

      if (data.user) {
        // Set admin status immediately for successful login
        setIsAdmin(true);
        // Check admin status in background (don't await to prevent blocking)
        checkAdminStatus(data.user.id).catch(console.error);
        toast.success("Welcome to Admin Panel!");
        return { success: true, user: data.user };
      }

      return { success: false, error: new Error("No user returned") };
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed - please try again");
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();

      if (error) {
        toast.error(error.message);
        return { success: false, error };
      }

      setIsAdmin(false);
      toast.success("Logged out successfully!");
      return { success: true };
    } catch (error) {
      toast.error("An unexpected error occurred");
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
        return { success: false, error };
      }

      if (data.user) {
        // Create admin user record
        const { error: adminError } = await supabase
          .from("admin_users")
          .insert({
            id: data.user.id,
            email: data.user.email!,
            role: "admin",
            is_active: true,
          });

        if (adminError) {
          console.error("Error creating admin user:", adminError);
        }

        toast.success("Admin account created successfully!");
        return { success: true, user: data.user };
      }

      return { success: false, error: new Error("No user returned") };
    } catch (error) {
      toast.error("An unexpected error occurred");
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    loading,
    isAdmin,
    signIn,
    signOut,
    signUp,
  };
};
