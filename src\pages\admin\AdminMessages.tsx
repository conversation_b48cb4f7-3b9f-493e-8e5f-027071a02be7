import React, { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  MessageSquare,
  Star,
  Mail,
  Search,
  Filter,
  MoreVertical,
  Reply,
  Archive,
  Trash2,
  Eye,
  Clock,
  User,
  Calendar,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { useMessages, useTestimonials } from "@/hooks/useMessages";

const AdminMessages = () => {
  const [activeTab, setActiveTab] = useState("messages");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [sortBy, setSortBy] = useState("newest");
  const [deletingMessageId, setDeletingMessageId] = useState<string | null>(
    null
  );

  // Database hooks
  const {
    messages,
    isLoading: messagesLoading,
    error: messagesError,
    updateMessage,
    deleteMessage,
    getMessageStats,
  } = useMessages();

  const {
    testimonials,
    isLoading: testimonialsLoading,
    error: testimonialsError,
    updateTestimonial,
    deleteTestimonial,
  } = useTestimonials();

  // Filter and search messages
  const filteredMessages = React.useMemo(() => {
    if (!messages) return [];

    return messages.filter((message) => {
      // Default behavior: hide archived messages unless specifically filtered
      if (filterStatus === "all" && message.status === "archived") {
        return false;
      }

      // Status filter
      if (filterStatus !== "all" && message.status !== filterStatus) {
        return false;
      }

      // Priority filter
      if (filterPriority !== "all" && message.priority !== filterPriority) {
        return false;
      }

      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          message.name.toLowerCase().includes(searchLower) ||
          message.email.toLowerCase().includes(searchLower) ||
          message.subject?.toLowerCase().includes(searchLower) ||
          message.message.toLowerCase().includes(searchLower) ||
          message.phone?.toLowerCase().includes(searchLower);

        if (!matchesSearch) {
          return false;
        }
      }

      return true;
    });
  }, [messages, filterStatus, filterPriority, searchTerm]);

  // Get real stats from database
  const stats = getMessageStats();
  const messageStats = [
    {
      title: "Total Messages",
      value: stats.total.toString(),
      change: "+12.5%",
      icon: MessageSquare,
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "New Messages",
      value: stats.new.toString(),
      change: "****%",
      icon: Mail,
      color: "from-red to-red/80",
    },
    {
      title: "Testimonials",
      value: testimonials?.length.toString() || "0",
      change: "+15.3%",
      icon: Star,
      color: "from-yellow-500 to-yellow-600",
    },
    {
      title: "Replied",
      value: stats.replied.toString(),
      change: "****%",
      icon: CheckCircle,
      color: "from-green-500 to-green-600",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-red/20 text-red border-red/30";
      case "read":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "replied":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "archived":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
      case "approved":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "pending":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red/30 text-red border-red/50 font-bold animate-pulse";
      case "high":
        return "bg-red/20 text-red border-red/30";
      case "medium":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "low":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const handleMessageAction = async (action: string, messageId: string) => {
    try {
      switch (action) {
        // Status actions
        case "mark-new":
          await updateMessage({ id: messageId, status: "new" });
          toast.success("Message marked as new");
          break;
        case "mark-read":
          await updateMessage({ id: messageId, status: "read" });
          toast.success("Message marked as read");
          break;
        case "mark-replied":
          await updateMessage({ id: messageId, status: "replied" });
          toast.success("Message marked as replied");
          break;
        case "archive":
          await updateMessage({ id: messageId, status: "archived" });
          toast.success("Message archived");
          break;

        // Priority actions
        case "priority-low":
          await updateMessage({ id: messageId, priority: "low" });
          toast.success("Priority set to low");
          break;
        case "priority-medium":
          await updateMessage({ id: messageId, priority: "medium" });
          toast.success("Priority set to medium");
          break;
        case "priority-high":
          await updateMessage({ id: messageId, priority: "high" });
          toast.success("Priority set to high");
          break;
        case "priority-urgent":
          await updateMessage({ id: messageId, priority: "urgent" });
          toast.success("Priority set to urgent");
          break;

        // Delete action - permanently removes from database
        case "delete":
          if (
            window.confirm(
              "Are you sure you want to permanently delete this message? This action cannot be undone."
            )
          ) {
            // Start deletion animation
            setDeletingMessageId(messageId);

            // Wait for animation to complete, then delete
            setTimeout(async () => {
              try {
                await deleteMessage(messageId);
                toast.success("Message permanently deleted");
              } catch (error) {
                console.error("Delete error:", error);
                toast.error("Failed to delete message");
              } finally {
                setDeletingMessageId(null);
              }
            }, 500);
          }
          break;

        default:
          console.warn("Unknown action:", action);
          break;
      }
    } catch (error) {
      console.error("Error handling message action:", error);
      toast.error("Failed to perform action");
    }
  };

  const renderMessages = () => {
    if (messagesLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="w-8 h-8 border-2 border-red/30 border-t-red rounded-full animate-spin"></div>
          <span className="ml-3 text-gray-400">Loading messages...</span>
        </div>
      );
    }

    if (messagesError) {
      return (
        <div className="text-center py-12">
          <AlertCircle className="w-12 h-12 text-red/50 mx-auto mb-4" />
          <p className="text-gray-400 mb-2">Failed to load messages</p>
          <p className="text-gray-500 text-sm">{messagesError.message}</p>
          <Button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red hover:bg-red/80"
          >
            Try Again
          </Button>
        </div>
      );
    }

    if (!filteredMessages || filteredMessages.length === 0) {
      // Show different empty states based on filters
      const isFiltered =
        filterStatus !== "all" || filterPriority !== "all" || searchTerm;

      if (isFiltered) {
        return (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gradient-to-br from-gray-500/20 to-gray-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-10 h-10 text-gray-500/50" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              No messages match your filters
            </h3>
            <p className="text-gray-400 mb-6 max-w-md mx-auto">
              Try adjusting your search terms or filters to see more messages.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={() => {
                  setFilterStatus("all");
                  setFilterPriority("all");
                  setSearchTerm("");
                }}
                className="bg-red hover:bg-red/80"
              >
                Clear All Filters
              </Button>
            </div>
          </div>
        );
      }
    }

    if (!messages || messages.length === 0) {
      return (
        <div className="text-center py-16">
          <div className="w-20 h-20 bg-gradient-to-br from-red/20 to-red/10 rounded-full flex items-center justify-center mx-auto mb-6">
            <MessageSquare className="w-10 h-10 text-red/50" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">
            No messages yet
          </h3>
          <p className="text-gray-400 mb-6 max-w-md mx-auto">
            When visitors submit the contact form on your portfolio, their
            messages will appear here.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={() => window.open("/", "_blank")}
              className="bg-red hover:bg-red/80"
            >
              <Eye className="w-4 h-4 mr-2" />
              View Portfolio
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="border-red/30 text-red hover:bg-red/10"
            >
              <div className="w-4 h-4 mr-2">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
              </div>
              Refresh
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {filteredMessages.map((message) => (
          <div
            key={message.id}
            className={`bg-dark-bg/50 border border-red/20 rounded-lg p-6 hover:border-red/40 transition-all duration-300 cursor-pointer transform ${
              deletingMessageId === message.id
                ? "scale-95 opacity-0 -translate-x-full pointer-events-none"
                : "hover:scale-[1.02] hover:shadow-lg"
            }`}
            onClick={() => setSelectedMessage(message)}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium">
                    {message.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="text-white font-semibold">{message.name}</h3>
                  <p className="text-gray-400 text-sm">{message.email}</p>
                  {message.phone && (
                    <p className="text-gray-400 text-xs">{message.phone}</p>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                {/* Priority Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div
                      className={`${getPriorityColor(
                        message.priority
                      )} cursor-pointer hover:opacity-80 px-2 py-1 rounded-md text-xs font-medium border`}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {message.priority}
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-dark-lighter border-red/20">
                    <DropdownMenuItem
                      onClick={() =>
                        handleMessageAction("priority-low", message.id)
                      }
                      className="text-green-400"
                    >
                      Low Priority
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        handleMessageAction("priority-medium", message.id)
                      }
                      className="text-yellow-400"
                    >
                      Medium Priority
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        handleMessageAction("priority-high", message.id)
                      }
                      className="text-red"
                    >
                      High Priority
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        handleMessageAction("priority-urgent", message.id)
                      }
                      className="text-red font-bold"
                    >
                      Urgent Priority
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Status Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <div
                      className={`${getStatusColor(
                        message.status
                      )} cursor-pointer hover:opacity-80 px-2 py-1 rounded-md text-xs font-medium border`}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {message.status}
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-dark-lighter border-red/20">
                    <DropdownMenuItem
                      onClick={() =>
                        handleMessageAction("mark-new", message.id)
                      }
                      className="text-red"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Mark as New
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        handleMessageAction("mark-read", message.id)
                      }
                      className="text-blue-400"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      Mark as Read
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() =>
                        handleMessageAction("mark-replied", message.id)
                      }
                      className="text-green-400"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Mark as Replied
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {/* Actions Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-white"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="bg-dark-lighter border-red/20">
                    <DropdownMenuItem
                      onClick={() => handleMessageAction("archive", message.id)}
                      className="text-yellow-400"
                    >
                      <Archive className="w-4 h-4 mr-2" />
                      Archive Message
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleMessageAction("delete", message.id)}
                      className="text-red hover:text-red"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Permanently
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <div className="mb-3">
              <h4 className="text-white font-medium mb-2">{message.subject}</h4>
              <p className="text-gray-300 line-clamp-2">{message.message}</p>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {new Date(message.created_at).toLocaleDateString()}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {new Date(message.created_at).toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTestimonials = () => {
    if (testimonialsLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="w-8 h-8 border-2 border-red/30 border-t-red rounded-full animate-spin"></div>
          <span className="ml-3 text-gray-400">Loading testimonials...</span>
        </div>
      );
    }

    if (testimonialsError) {
      return (
        <div className="text-center py-12">
          <AlertCircle className="w-12 h-12 text-red/50 mx-auto mb-4" />
          <p className="text-gray-400 mb-2">Failed to load testimonials</p>
          <p className="text-gray-500 text-sm">{testimonialsError.message}</p>
          <Button
            onClick={() => window.location.reload()}
            className="mt-4 bg-red hover:bg-red/80"
          >
            Try Again
          </Button>
        </div>
      );
    }

    if (!testimonials || testimonials.length === 0) {
      return (
        <div className="text-center py-16">
          <div className="w-20 h-20 bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 rounded-full flex items-center justify-center mx-auto mb-6">
            <Star className="w-10 h-10 text-yellow-500/50" />
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">
            No testimonials yet
          </h3>
          <p className="text-gray-400 mb-6 max-w-md mx-auto">
            Client testimonials and reviews will appear here when they're
            submitted.
          </p>
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
            className="border-yellow-500/30 text-yellow-500 hover:bg-yellow-500/10"
          >
            <div className="w-4 h-4 mr-2">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
            </div>
            Refresh
          </Button>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {testimonials.map((testimonial) => (
          <div
            key={testimonial.id}
            className="bg-dark-bg/50 border border-red/20 rounded-lg p-6 hover:border-red/40 transition-all"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center">
                  <Star className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-white font-semibold">
                    {testimonial.name}
                  </h3>
                  <p className="text-gray-400 text-sm">{testimonial.company}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < testimonial.rating
                          ? "text-yellow-500 fill-current"
                          : "text-gray-600"
                      }`}
                    />
                  ))}
                </div>
                <Badge className={getStatusColor(testimonial.status)}>
                  {testimonial.status}
                </Badge>
              </div>
            </div>

            <p className="text-gray-300 mb-3">{testimonial.message}</p>

            <div className="flex items-center justify-between text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {testimonial.date}
              </div>
              <span className="text-red">{testimonial.project}</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <AdminLayout title="Messages & Feedback">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h2 className="text-2xl font-bold text-white">Messages & Feedback</h2>
          <p className="text-gray-400">
            Manage contact messages and testimonials
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {messageStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="bg-dark-lighter border border-red/20 rounded-xl p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <div
                    className={`w-12 h-12 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center`}
                  >
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-green-400 text-sm font-medium">
                    {stat.change}
                  </span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">
                    {stat.value}
                  </h3>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Tabs and Search */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
          {/* Results Count */}
          {activeTab === "messages" && filteredMessages && (
            <div className="mb-4 text-sm text-gray-400">
              Showing {filteredMessages.length} of {messages?.length || 0}{" "}
              messages
              {(filterStatus !== "all" ||
                filterPriority !== "all" ||
                searchTerm) && (
                <span className="ml-2 text-red">(filtered)</span>
              )}
            </div>
          )}

          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
            <div className="flex gap-2">
              <Button
                onClick={() => setActiveTab("messages")}
                className={
                  activeTab === "messages"
                    ? "bg-red hover:bg-red/80"
                    : "bg-dark-bg hover:bg-dark-bg/80"
                }
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Messages
              </Button>
              <Button
                onClick={() => setActiveTab("testimonials")}
                className={
                  activeTab === "testimonials"
                    ? "bg-red hover:bg-red/80"
                    : "bg-dark-bg hover:bg-dark-bg/80"
                }
              >
                <Star className="w-4 h-4 mr-2" />
                Testimonials
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search messages..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-dark-bg border-red/30 text-white"
                />
              </div>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 bg-dark-bg border border-red/30 rounded-lg text-white text-sm"
              >
                <option value="all">All Status</option>
                <option value="new">New</option>
                <option value="read">Read</option>
                <option value="replied">Replied</option>
                <option value="archived">Archived</option>
              </select>

              <select
                value={filterPriority}
                onChange={(e) => setFilterPriority(e.target.value)}
                className="px-3 py-2 bg-dark-bg border border-red/30 rounded-lg text-white text-sm"
              >
                <option value="all">All Priority</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
          </div>

          {activeTab === "messages" ? renderMessages() : renderTestimonials()}
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminMessages;
