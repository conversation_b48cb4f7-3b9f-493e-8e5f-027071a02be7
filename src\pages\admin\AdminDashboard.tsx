import React from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  Users,
  MessageSquare,
  Eye,
  TrendingUp,
  FileText,
  Star,
  Activity,
  Calendar,
  BarChart3,
  PieChart,
} from "lucide-react";

const AdminDashboard = () => {
  const stats = [
    {
      title: "Total Visitors",
      value: "12,847",
      change: "+12.5%",
      trend: "up",
      icon: Eye,
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "Messages Received",
      value: "284",
      change: "+8.2%",
      trend: "up",
      icon: MessageSquare,
      color: "from-green-500 to-green-600",
    },
    {
      title: "Portfolio Views",
      value: "5,632",
      change: "+15.3%",
      trend: "up",
      icon: FileText,
      color: "from-purple-500 to-purple-600",
    },
    {
      title: "Testimonials",
      value: "47",
      change: "+3.1%",
      trend: "up",
      icon: Star,
      color: "from-red to-red/80",
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: "message",
      title: "New contact message from <PERSON>",
      time: "2 minutes ago",
      icon: MessageSquare,
    },
    {
      id: 2,
      type: "view",
      title: "Portfolio project viewed 15 times",
      time: "1 hour ago",
      icon: Eye,
    },
    {
      id: 3,
      type: "testimonial",
      title: "New testimonial submitted",
      time: "3 hours ago",
      icon: Star,
    },
    {
      id: 4,
      type: "user",
      title: "New user registration",
      time: "5 hours ago",
      icon: Users,
    },
  ];

  const quickActions = [
    {
      title: "Edit Hero Section",
      description: "Update main banner content",
      icon: FileText,
      action: "/admin/content",
      color: "from-blue-500 to-blue-600",
    },
    {
      title: "Manage Messages",
      description: "Review contact messages",
      icon: MessageSquare,
      action: "/admin/messages",
      color: "from-green-500 to-green-600",
    },
    {
      title: "User Management",
      description: "Manage user accounts",
      icon: Users,
      action: "/admin/users",
      color: "from-purple-500 to-purple-600",
    },
    {
      title: "Site Settings",
      description: "Configure site options",
      icon: Activity,
      action: "/admin/settings",
      color: "from-red to-red/80",
    },
  ];

  return (
    <AdminLayout title="Dashboard Overview">
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-red/20 to-red/10 border border-red/30 rounded-xl p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center">
              <Activity className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">
                Welcome back, Admin!
              </h2>
              <p className="text-gray-300">
                Here's what's happening with your portfolio today.
              </p>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div
                key={index}
                className="bg-dark-lighter border border-red/20 rounded-xl p-6 hover:border-red/40 transition-all duration-300"
              >
                <div className="flex items-center justify-between mb-4">
                  <div
                    className={`w-12 h-12 bg-gradient-to-br ${stat.color} rounded-lg flex items-center justify-center`}
                  >
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <span className="text-green-400 text-sm font-medium flex items-center gap-1">
                    <TrendingUp className="w-4 h-4" />
                    {stat.change}
                  </span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-1">
                    {stat.value}
                  </h3>
                  <p className="text-gray-400 text-sm">{stat.title}</p>
                </div>
              </div>
            );
          })}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Activity */}
          <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-6">
              <Activity className="w-5 h-5 text-red" />
              <h3 className="text-lg font-semibold text-white">
                Recent Activity
              </h3>
            </div>
            <div className="space-y-4">
              {recentActivities.map((activity) => {
                const Icon = activity.icon;
                return (
                  <div
                    key={activity.id}
                    className="flex items-center gap-4 p-3 bg-dark-bg/50 rounded-lg"
                  >
                    <div className="w-10 h-10 bg-red/20 rounded-lg flex items-center justify-center">
                      <Icon className="w-5 h-5 text-red" />
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">{activity.title}</p>
                      <p className="text-gray-400 text-xs">{activity.time}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-6">
              <BarChart3 className="w-5 h-5 text-red" />
              <h3 className="text-lg font-semibold text-white">
                Quick Actions
              </h3>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <button
                    key={index}
                    className="p-4 bg-dark-bg/50 rounded-lg border border-red/10 hover:border-red/30 transition-all duration-300 text-left group"
                  >
                    <div
                      className={`w-10 h-10 bg-gradient-to-br ${action.color} rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform`}
                    >
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <h4 className="text-white font-medium mb-1">
                      {action.title}
                    </h4>
                    <p className="text-gray-400 text-xs">
                      {action.description}
                    </p>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Analytics Chart Placeholder */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <PieChart className="w-5 h-5 text-red" />
              <h3 className="text-lg font-semibold text-white">
                Analytics Overview
              </h3>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-400">Last 30 days</span>
            </div>
          </div>

          {/* Enhanced Analytics Chart */}
          <div className="h-64 bg-dark-bg/50 rounded-lg border border-red/10 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">
                Visitor Analytics
              </h3>
              <div className="flex gap-2">
                <button className="px-3 py-1 text-xs bg-red/20 text-red rounded-full">
                  7D
                </button>
                <button className="px-3 py-1 text-xs text-gray-400 hover:text-white">
                  30D
                </button>
                <button className="px-3 py-1 text-xs text-gray-400 hover:text-white">
                  90D
                </button>
              </div>
            </div>

            {/* Mock Chart Data */}
            <div className="h-40 flex items-end justify-between gap-2">
              {[65, 45, 78, 52, 89, 67, 94].map((height, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div
                    className="w-full bg-gradient-to-t from-red to-red/60 rounded-t-sm transition-all duration-500 hover:from-red/80 hover:to-red/40"
                    style={{ height: `${height}%` }}
                  ></div>
                  <span className="text-xs text-gray-400 mt-2">
                    {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"][index]}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
