import React, { useState, useEffect } from "react";
import { Star, Quote, Plus, MessageSquare } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTestimonials } from "@/hooks/useMessages";
import TestimonialForm from "./TestimonialForm";

const Testimonials = () => {
  const [visibleCards, setVisibleCards] = useState<string[]>([]);
  const [isFormOpen, setIsFormOpen] = useState(false);

  // Get real testimonials from database
  const { testimonials: dbTestimonials, isLoading } = useTestimonials();

  // Filter only approved testimonials for public display
  const approvedTestimonials =
    dbTestimonials?.filter(
      (testimonial) => testimonial.status === "approved"
    ) || [];

  // Debug logging
  React.useEffect(() => {
    console.log("Testimonials Debug:");
    console.log("- isLoading:", isLoading);
    console.log("- dbTestimonials:", dbTestimonials);
    console.log("- approvedTestimonials:", approvedTestimonials);
    console.log("- final testimonials to show:", testimonials);
  }, [isLoading, dbTestimonials, approvedTestimonials, testimonials]);

  // Fallback testimonials if no approved ones exist
  const fallbackTestimonials = [
    {
      id: "fallback-1",
      name: "Jared Warner",
      position: "CEO",
      company: "Apple Inc.",
      image_url:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&q=80",
      message:
        "John was a real pleasure to work with and we look forward to working with him again. He's definitely the kind of designer you can trust with a project from start to finish.",
      rating: 5,
      featured: true,
      status: "approved",
      created_at: new Date().toISOString(),
    },
    {
      id: "fallback-2",
      name: "Emily Parker",
      position: "CTO",
      company: "Google LLC",
      image_url:
        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=300&h=300&fit=crop&q=80",
      message:
        "Highly recommend John. His expertise in web development is exceptional. He delivered beyond our expectations and was a pleasure to work with throughout the project.",
      rating: 5,
      featured: true,
      status: "approved",
      created_at: new Date().toISOString(),
    },
    {
      id: "fallback-3",
      name: "Michael Thompson",
      position: "Marketing Director",
      company: "TechCorp",
      image_url:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=300&fit=crop&q=80",
      message:
        "Working with John was an excellent experience. He understood our requirements perfectly and delivered a beautiful website that exceeded our expectations.",
      rating: 4,
      featured: false,
      status: "approved",
      created_at: new Date().toISOString(),
    },
  ];

  // Use real testimonials if available, otherwise use fallback
  const testimonials =
    approvedTestimonials.length > 0
      ? approvedTestimonials
      : fallbackTestimonials;

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cardId = entry.target.getAttribute("data-card-id") || "";
            if (cardId && !visibleCards.includes(cardId)) {
              setVisibleCards((prev) => [...prev, cardId]);
            }
          }
        });
      },
      { threshold: 0.1 }
    );

    const cards = document.querySelectorAll(".testimonial-card");
    cards.forEach((card) => observer.observe(card));

    return () => observer.disconnect();
  }, [visibleCards]);

  return (
    <section
      id="testimonials"
      className="section bg-dark-darker relative overflow-hidden"
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        <div className="testimonials-bg-gradient"></div>
        <div className="testimonials-floating-particles"></div>
      </div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="enhanced-testimonials-header">
            <h2 className="enhanced-testimonials-title">
              <span className="testimonials-title-text">Testimonials</span>
              <div className="testimonials-title-glow"></div>
            </h2>
            <div className="testimonials-description-container">
              <p className="testimonials-description">
                What my clients say about my work and expertise.
              </p>
              <div className="testimonials-description-accent"></div>
            </div>
          </div>

          {/* Add Testimonial Button */}
          <div className="mt-8">
            <Button
              onClick={() => setIsFormOpen(true)}
              className="bg-gradient-to-r from-red to-red/80 hover:from-red/90 hover:to-red/70 text-white px-8 py-3 rounded-full font-medium transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-red/25"
            >
              <MessageSquare className="w-5 h-5 mr-2" />
              Share Your Experience
            </Button>
          </div>
        </div>

        {/* Debug Info */}
        {isLoading && (
          <div className="text-center text-gray-400 mb-8">
            Loading testimonials...
          </div>
        )}

        {!isLoading && (
          <div className="text-center text-gray-400 mb-4 text-sm">
            Found {dbTestimonials?.length || 0} total testimonials,{" "}
            {approvedTestimonials.length} approved
          </div>
        )}

        {/* Enhanced Testimonials Grid */}
        <div className="enhanced-testimonials-grid">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              data-card-id={testimonial.id}
              className="testimonial-card enhanced-testimonial-card visible"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Card Background Effects */}
              <div className="testimonial-card-bg"></div>
              <div className="testimonial-card-glow"></div>
              <div className="testimonial-card-particles"></div>

              {/* Quote Icon */}
              <div className="testimonial-quote-icon">
                <Quote size={24} className="text-red opacity-60" />
              </div>

              {/* Enhanced Rating */}
              <div className="enhanced-rating-container">
                <div className="rating-stars">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="star-wrapper">
                      <Star
                        size={18}
                        className={`enhanced-star ${
                          i < testimonial.rating ? "filled" : "empty"
                        }`}
                      />
                    </div>
                  ))}
                </div>
                <div className="rating-glow"></div>
              </div>

              {/* Enhanced Text */}
              <div className="testimonial-text-container">
                <p className="enhanced-testimonial-text">
                  {testimonial.message}
                </p>
              </div>

              {/* Enhanced Client Info */}
              <div className="enhanced-client-info">
                <div className="client-avatar-container">
                  <div className="client-avatar-glow"></div>
                  <div className="client-avatar">
                    <img
                      src={
                        testimonial.image_url ||
                        `https://ui-avatars.com/api/?name=${encodeURIComponent(
                          testimonial.name
                        )}&background=ef4444&color=fff&size=300`
                      }
                      alt={testimonial.name}
                      className="avatar-image"
                      loading="lazy"
                    />
                  </div>
                  <div className="avatar-ring"></div>
                </div>

                <div className="client-details">
                  <h5 className="client-name">{testimonial.name}</h5>
                  <p className="client-position">{testimonial.position}</p>
                  <p className="client-company">{testimonial.company}</p>

                  {/* Featured Badge */}
                  {testimonial.featured && (
                    <div className="project-badge">
                      <span className="project-name">Featured Review</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Hover Effects */}
              <div className="testimonial-hover-overlay"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Testimonial Form Modal */}
      <TestimonialForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
      />
    </section>
  );
};

export default Testimonials;
