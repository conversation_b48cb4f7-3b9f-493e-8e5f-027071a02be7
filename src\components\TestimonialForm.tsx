import React, { useState } from "react";
import {
  Star,
  Send,
  User,
  Building,
  Briefcase,
  MessageSquare,
  CheckCircle,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useTestimonials } from "@/hooks/useMessages";

interface TestimonialFormProps {
  isOpen: boolean;
  onClose: () => void;
}

const TestimonialForm: React.FC<TestimonialFormProps> = ({ isOpen, onClose }) => {
  const { createTestimonial, isCreating } = useTestimonials();
  
  const [formData, setFormData] = useState({
    name: "",
    company: "",
    position: "",
    message: "",
    rating: 5,
  });

  const [hoveredRating, setHoveredRating] = useState(0);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.name || !formData.company || !formData.message) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (formData.message.length < 10) {
      toast.error("Please provide a more detailed testimonial (at least 10 characters)");
      return;
    }

    try {
      await createTestimonial({
        ...formData,
        status: "pending",
        featured: false,
      });

      // Reset form
      setFormData({
        name: "",
        company: "",
        position: "",
        message: "",
        rating: 5,
      });

      toast.success("Thank you for your testimonial!", {
        description: "Your testimonial has been submitted and is pending approval.",
        duration: 5000,
      });

      onClose();
    } catch (error) {
      console.error("Error submitting testimonial:", error);
      toast.error("Failed to submit testimonial. Please try again.");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-dark-lighter border border-red/20 rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-red to-red/70 rounded-full flex items-center justify-center">
              <Star className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white">Share Your Experience</h2>
              <p className="text-gray-400">Help others by sharing your testimonial</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating */}
          <div className="space-y-2">
            <label className="text-white font-medium flex items-center gap-2">
              <Star className="w-4 h-4" />
              Rating *
            </label>
            <div className="flex items-center gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  className="p-1 transition-transform hover:scale-110"
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  onClick={() => setFormData(prev => ({ ...prev, rating: star }))}
                >
                  <Star
                    className={`w-8 h-8 transition-colors ${
                      star <= (hoveredRating || formData.rating)
                        ? "text-yellow-500 fill-current"
                        : "text-gray-600"
                    }`}
                  />
                </button>
              ))}
              <span className="ml-3 text-gray-400">
                {formData.rating} star{formData.rating !== 1 ? 's' : ''}
              </span>
            </div>
          </div>

          {/* Name and Company */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-white font-medium flex items-center gap-2">
                <User className="w-4 h-4" />
                Your Name *
              </label>
              <Input
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Enter your full name"
                className="bg-dark-bg border-red/30 text-white placeholder:text-gray-500"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-white font-medium flex items-center gap-2">
                <Building className="w-4 h-4" />
                Company *
              </label>
              <Input
                name="company"
                value={formData.company}
                onChange={handleChange}
                placeholder="Your company name"
                className="bg-dark-bg border-red/30 text-white placeholder:text-gray-500"
                required
              />
            </div>
          </div>

          {/* Position */}
          <div className="space-y-2">
            <label className="text-white font-medium flex items-center gap-2">
              <Briefcase className="w-4 h-4" />
              Position (Optional)
            </label>
            <Input
              name="position"
              value={formData.position}
              onChange={handleChange}
              placeholder="Your job title or position"
              className="bg-dark-bg border-red/30 text-white placeholder:text-gray-500"
            />
          </div>

          {/* Testimonial Message */}
          <div className="space-y-2">
            <label className="text-white font-medium flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              Your Testimonial *
            </label>
            <Textarea
              name="message"
              value={formData.message}
              onChange={handleChange}
              placeholder="Share your experience working with me. What did you like? How did I help your business?"
              rows={5}
              className="bg-dark-bg border-red/30 text-white placeholder:text-gray-500 resize-none"
              required
            />
            <div className="text-right text-sm text-gray-400">
              {formData.message.length}/500 characters
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex gap-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 border-red/30 text-red hover:bg-red/10"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isCreating}
              className="flex-1 bg-red hover:bg-red/80 text-white"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                  Submitting...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4 mr-2" />
                  Submit Testimonial
                </>
              )}
            </Button>
          </div>
        </form>

        {/* Footer Note */}
        <div className="mt-6 p-4 bg-dark-bg/50 rounded-lg border border-red/10">
          <div className="flex items-start gap-3">
            <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-gray-400">
              <p className="font-medium text-white mb-1">Review Process</p>
              <p>
                Your testimonial will be reviewed before being published on the website. 
                This helps maintain quality and authenticity. You'll be notified once it's approved.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialForm;
