-- Fix storage policy for testimonial image uploads
-- Run this in Supabase SQL Editor immediately

-- Drop the existing restrictive policy
DROP POLICY IF EXISTS "Allow authenticated users to upload testimonial images" ON storage.objects;

-- Create a more permissive policy for public testimonial submissions
CREATE POLICY "Allow anyone to upload testimonial images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'testimonial-images'
);

-- Verify the policy was created
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'objects' AND policyname LIKE '%testimonial%';
