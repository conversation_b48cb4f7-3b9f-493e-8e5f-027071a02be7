-- Update contact_messages table for enhanced admin functionality
-- Run this in Supabase SQL Editor to update existing database

-- Add phone column if it doesn't exist
ALTER TABLE contact_messages
ADD COLUMN IF NOT EXISTS phone TEXT;

-- Update constraints to ensure all priority and status options are supported
ALTER TABLE contact_messages
DROP CONSTRAINT IF EXISTS contact_messages_status_check;

ALTER TABLE contact_messages
ADD CONSTRAINT contact_messages_status_check
CHECK (status IN ('new', 'read', 'replied', 'archived'));

ALTER TABLE contact_messages
DROP CONSTRAINT IF EXISTS contact_messages_priority_check;

ALTER TABLE contact_messages
ADD CONSTRAINT contact_messages_priority_check
CHECK (priority IN ('low', 'medium', 'high', 'urgent'));

-- Update the table comment
COMMENT ON COLUMN contact_messages.phone IS 'Optional phone number for contact messages';
COMMENT ON COLUMN contact_messages.status IS 'Message status: new, read, replied, archived';
COMMENT ON COLUMN contact_messages.priority IS 'Message priority: low, medium, high, urgent';
