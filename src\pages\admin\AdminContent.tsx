import React, { useState } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import {
  Save,
  Edit3,
  Image,
  Type,
  Palette,
  FileText,
  User,
  Briefcase,
  Star,
  MessageSquare,
  Plus,
  Trash2,
  Eye,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

const AdminContent = () => {
  const [activeTab, setActiveTab] = useState("hero");
  const [isEditing, setIsEditing] = useState(false);

  const contentSections = [
    { id: "hero", label: "Hero Section", icon: Type },
    { id: "about", label: "About Me", icon: User },
    { id: "skills", label: "Skills", icon: Star },
    { id: "portfolio", label: "Portfolio", icon: Briefcase },
    { id: "testimonials", label: "Testimonials", icon: MessageSquare },
    { id: "contact", label: "Contact Info", icon: MessageSquare },
  ];

  const [heroContent, setHeroContent] = useState({
    name: "<PERSON>",
    profession: "Web Developer",
    welcomeText: "WELCOME TO MY WORLD",
    description:
      "I'm a web designer and developer based in USA. I specialize in building exceptional digital experiences.",
    buttonText: "More About Me",
  });

  const [aboutContent, setAboutContent] = useState({
    title: "About Me",
    description:
      "I'm a passionate web developer with 5+ years of experience creating digital solutions.",
    experience: "5+ Years",
    projects: "100+ Projects",
    clients: "50+ Clients",
  });

  const [skills, setSkills] = useState([
    { name: "HTML & CSS", percentage: 95 },
    { name: "JavaScript", percentage: 89 },
    { name: "React.js", percentage: 90 },
    { name: "Node.js", percentage: 82 },
  ]);

  const [portfolioItems, setPortfolioItems] = useState([
    {
      id: 1,
      title: "E-commerce Platform",
      description: "Modern e-commerce solution with React and Node.js",
      category: "Web Development",
      image: "/api/placeholder/400/300",
      technologies: ["React", "Node.js", "MongoDB"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: true,
    },
    {
      id: 2,
      title: "Mobile Banking App",
      description: "Secure mobile banking application with modern UI",
      category: "Mobile App",
      image: "/api/placeholder/400/300",
      technologies: ["React Native", "Firebase"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: false,
    },
  ]);

  const [testimonials, setTestimonials] = useState([
    {
      id: 1,
      name: "Alex Thompson",
      company: "TechStart Inc.",
      position: "CEO",
      message:
        "John delivered an exceptional website that exceeded our expectations.",
      rating: 5,
      image: "/api/placeholder/60/60",
      featured: true,
    },
  ]);

  const [contactInfo, setContactInfo] = useState({
    phone: "+************",
    email: "<EMAIL>",
    address: "123 Main St, City, Country",
    socialLinks: {
      linkedin: "https://linkedin.com/in/example",
      github: "https://github.com/example",
      twitter: "https://twitter.com/example",
    },
    availability: "Available for freelance work",
  });

  const [portfolioItems, setPortfolioItems] = useState([
    {
      id: 1,
      title: "E-commerce Platform",
      description: "Modern e-commerce solution with React and Node.js",
      category: "Web Development",
      image: "/api/placeholder/400/300",
      technologies: ["React", "Node.js", "MongoDB"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: true,
    },
    {
      id: 2,
      title: "Mobile Banking App",
      description: "Secure mobile banking application with modern UI",
      category: "Mobile App",
      image: "/api/placeholder/400/300",
      technologies: ["React Native", "Firebase"],
      liveUrl: "https://example.com",
      githubUrl: "https://github.com/example",
      featured: false,
    },
  ]);

  const [testimonials, setTestimonials] = useState([
    {
      id: 1,
      name: "Alex Thompson",
      company: "TechStart Inc.",
      position: "CEO",
      message:
        "John delivered an exceptional website that exceeded our expectations.",
      rating: 5,
      image: "/api/placeholder/60/60",
      featured: true,
    },
    {
      id: 2,
      name: "Lisa Chen",
      company: "Creative Agency",
      position: "Design Director",
      message: "Professional, creative, and delivered on time.",
      rating: 5,
      image: "/api/placeholder/60/60",
      featured: false,
    },
  ]);

  const [contactInfo, setContactInfo] = useState({
    phone: "+************",
    email: "<EMAIL>",
    address: "123 Main St, City, Country",
    socialLinks: {
      linkedin: "https://linkedin.com/in/example",
      github: "https://github.com/example",
      twitter: "https://twitter.com/example",
      instagram: "https://instagram.com/example",
    },
    availability: "Available for freelance work",
    timezone: "EST (UTC-5)",
  });

  const handleSave = () => {
    // Simulate saving to database
    toast.success("Content updated successfully!");
    setIsEditing(false);
  };

  const renderHeroEditor = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Name
          </label>
          <Input
            value={heroContent.name}
            onChange={(e) =>
              setHeroContent({ ...heroContent, name: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Profession
          </label>
          <Input
            value={heroContent.profession}
            onChange={(e) =>
              setHeroContent({ ...heroContent, profession: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Welcome Text
        </label>
        <Input
          value={heroContent.welcomeText}
          onChange={(e) =>
            setHeroContent({ ...heroContent, welcomeText: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Description
        </label>
        <Textarea
          value={heroContent.description}
          onChange={(e) =>
            setHeroContent({ ...heroContent, description: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white min-h-[100px]"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Button Text
        </label>
        <Input
          value={heroContent.buttonText}
          onChange={(e) =>
            setHeroContent({ ...heroContent, buttonText: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
        />
      </div>
    </div>
  );

  const renderAboutEditor = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Section Title
        </label>
        <Input
          value={aboutContent.title}
          onChange={(e) =>
            setAboutContent({ ...aboutContent, title: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Description
        </label>
        <Textarea
          value={aboutContent.description}
          onChange={(e) =>
            setAboutContent({ ...aboutContent, description: e.target.value })
          }
          className="bg-dark-bg border-red/30 text-white min-h-[120px]"
          disabled={!isEditing}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Experience
          </label>
          <Input
            value={aboutContent.experience}
            onChange={(e) =>
              setAboutContent({ ...aboutContent, experience: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Projects
          </label>
          <Input
            value={aboutContent.projects}
            onChange={(e) =>
              setAboutContent({ ...aboutContent, projects: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Clients
          </label>
          <Input
            value={aboutContent.clients}
            onChange={(e) =>
              setAboutContent({ ...aboutContent, clients: e.target.value })
            }
            className="bg-dark-bg border-red/30 text-white"
            disabled={!isEditing}
          />
        </div>
      </div>
    </div>
  );

  const renderSkillsEditor = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Skills Management</h3>
        {isEditing && (
          <Button
            onClick={() => setSkills([...skills, { name: "", percentage: 0 }])}
            className="bg-red hover:bg-red/80"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Skill
          </Button>
        )}
      </div>

      <div className="space-y-4">
        {skills.map((skill, index) => (
          <div
            key={index}
            className="flex items-center gap-4 p-4 bg-dark-bg/50 rounded-lg border border-red/20"
          >
            <div className="flex-1">
              <Input
                value={skill.name}
                onChange={(e) => {
                  const newSkills = [...skills];
                  newSkills[index].name = e.target.value;
                  setSkills(newSkills);
                }}
                placeholder="Skill name"
                className="bg-dark-bg border-red/30 text-white mb-2"
                disabled={!isEditing}
              />
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  min="0"
                  max="100"
                  value={skill.percentage}
                  onChange={(e) => {
                    const newSkills = [...skills];
                    newSkills[index].percentage = parseInt(e.target.value) || 0;
                    setSkills(newSkills);
                  }}
                  className="bg-dark-bg border-red/30 text-white w-20"
                  disabled={!isEditing}
                />
                <span className="text-gray-400">%</span>
              </div>
            </div>
            {isEditing && (
              <Button
                onClick={() => setSkills(skills.filter((_, i) => i !== index))}
                variant="destructive"
                size="sm"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case "hero":
        return renderHeroEditor();
      case "about":
        return renderAboutEditor();
      case "skills":
        return renderSkillsEditor();
      default:
        return (
          <div className="text-center py-12">
            <FileText className="w-12 h-12 text-red/50 mx-auto mb-4" />
            <p className="text-gray-400">
              Content editor for {activeTab} section coming soon...
            </p>
          </div>
        );
    }
  };

  return (
    <AdminLayout title="Content Management">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-white">
              Content Management
            </h2>
            <p className="text-gray-400">
              Edit and manage your portfolio content
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              onClick={() => window.open("/", "_blank")}
              variant="outline"
              className="border-red/30 text-red hover:bg-red/10"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview Site
            </Button>
            {isEditing ? (
              <div className="flex gap-2">
                <Button
                  onClick={handleSave}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </Button>
                <Button
                  onClick={() => setIsEditing(false)}
                  variant="outline"
                  className="border-gray-600 text-gray-400"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <Button
                onClick={() => setIsEditing(true)}
                className="bg-red hover:bg-red/80"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Edit Content
              </Button>
            )}
          </div>
        </div>

        {/* Content Tabs */}
        <div className="bg-dark-lighter border border-red/20 rounded-xl overflow-hidden">
          <div className="flex overflow-x-auto border-b border-red/20">
            {contentSections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveTab(section.id)}
                  className={`flex items-center gap-2 px-6 py-4 whitespace-nowrap transition-all ${
                    activeTab === section.id
                      ? "bg-red/20 text-red border-b-2 border-red"
                      : "text-gray-400 hover:text-white hover:bg-red/10"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {section.label}
                </button>
              );
            })}
          </div>

          <div className="p-6">{renderContent()}</div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminContent;
