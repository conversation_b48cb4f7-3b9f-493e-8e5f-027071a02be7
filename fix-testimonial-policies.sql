-- Fix testimonial policies for proper admin access
-- Run this in Supabase SQL Editor

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Only admins can modify testimonials" ON testimonials;
DROP POLICY IF EXISTS "Admins can update testimonials" ON testimonials;
DROP POLICY IF EXISTS "Ad<PERSON> can delete testimonials" ON testimonials;

-- Create proper policies for admin access
CREATE POLICY "Ad<PERSON> can update testimonials" ON testimonials
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM admin_users au 
            WHERE au.id = auth.uid() AND au.is_active = true
        )
    );

CREATE POLICY "Admins can delete testimonials" ON testimonials
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM admin_users au 
            WHERE au.id = auth.uid() AND au.is_active = true
        )
    );

-- Verify policies are created correctly
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'testimonials'
ORDER BY policyname;
