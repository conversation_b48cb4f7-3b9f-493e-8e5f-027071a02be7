-- Fix storage policy for testimonial image uploads (Version 2)
-- Run this in Supabase SQL Editor

-- Drop both possible policy names to avoid conflicts
DROP POLICY IF EXISTS "Allow authenticated users to upload testimonial images" ON storage.objects;
DROP POLICY IF EXISTS "Allow anyone to upload testimonial images" ON storage.objects;

-- Create the correct policy
CREATE POLICY "Allow anyone to upload testimonial images" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'testimonial-images');

-- Verify the policy was created
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'objects' AND policyname LIKE '%testimonial%';
