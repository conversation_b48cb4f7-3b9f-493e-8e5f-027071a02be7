// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://xxawmsmnsgadqagkfuqp.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh4YXdtc21uc2dhZHFhZ2tmdXFwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODI1NTAsImV4cCI6MjA2NDM1ODU1MH0.pUz7Nm0JiG_gaz7OAmq47IrZDt-HAjXJvryvT17E1Bo";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY
);
